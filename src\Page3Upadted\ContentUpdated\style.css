.contant2 {
  align-items: center;
  background-color: #fbf9f4;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 1280px;
}

.contant2 .header {
  align-items: flex-start;
  align-self: stretch;
  background-color: transparent;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.contant2 .row {
  align-items: center;
  align-self: stretch;
  background-color: #0e5447;
  display: flex;
  gap: 691px;
  height: 57px;
  padding: 16px 48px;
  position: relative;
  width: 100%;
}

.contant2 .div {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 20px;
  position: relative;
}

.contant2 .depository-trust {
  height: 25px;
  overflow: hidden;
  position: relative;
  width: 65px;
}

.contant2 .logo {
  color: #ffffff;
  font-family: "Fugaz One-Regular", Helvetica;
  font-size: 24px;
  font-weight: 400;
  left: 1px;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  top: -5px;
  width: 68px;
}

.contant2 .line {
  height: 20px !important;
  object-fit: cover !important;
  position: relative !important;
  width: 1px !important;
}

.contant2 .title {
  color: #ffffff;
  flex: 1;
  font-family: var(--application-header-font-family);
  font-size: var(--application-header-font-size);
  font-style: var(--application-header-font-style);
  font-weight: var(--application-header-font-weight);
  letter-spacing: var(--application-header-letter-spacing);
  line-height: var(--application-header-line-height);
  position: relative;
}

.contant2 .div-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: flex-end;
  position: relative;
}

.contant2 .frame-content2 {
  align-items: flex-end;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  position: relative;
  width: 209px;
}

.contant2 .tool-bar-icons {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 16px;
  justify-content: flex-end;
  position: relative;
}

.contant2 .search {
  background: none;
  border: none;
  color: #ffffff;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 20px;
  font-weight: 400;
  height: 20px;
  letter-spacing: 0;
  line-height: normal;
  padding: 0;
  padding-left: 0;
  position: relative;
  text-align: center;
  white-space: nowrap;
  width: 20px;
}

.contant2 .div-wrapper {
  height: 20px;
  position: relative;
  width: 20px;
}

.contant2 .text-wrapper-content2 {
  color: #ffffff;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 20px;
  font-weight: 400;
  left: 0;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  text-align: center;
  top: -2px;
  white-space: nowrap;
}

.contant2 .menu {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  position: relative;
}

.contant2 .menu-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  position: relative;
}

.contant2 .th {
  color: #ffffff;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 20px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  text-align: center;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .logout {
  height: 14.97px;
  position: relative;
  width: 20px;
}

.contant2 .logout-2 {
  height: 15px;
  position: relative;
  width: 20px;
}

.contant2 .navigation {
  align-items: center;
  background-color: #ffffff;
  display: flex;
  gap: 40px;
  height: 41px;
  padding: 0px 48px;
  position: relative;
  width: 1280px;
}

.contant2 .tab-item {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-contant2: center;
  padding: 12.5px 0px;
  position: relative;
}

.contant2 .text-wrapper-content2-2 {
  color: #616161;
  font-family: var(--tab-header-font-family);
  font-size: var(--tab-header-font-size);
  font-style: var(--tab-header-font-style);
  font-weight: var(--tab-header-font-weight);
  letter-spacing: var(--tab-header-letter-spacing);
  line-height: var(--tab-header-line-height);
  margin-top: -3.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .NAVIGATION-ONE-wrapper {
  align-items: center;
  border-bottom-style: solid;
  border-bottom-width: 3px;
  border-color: #ffc454;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-contant2: center;
  padding: 12.5px 0px;
  position: relative;
}

.contant2 .NAVIGATION-ONE {
  color: #212121;
  font-family: var(--tab-header-font-family);
  font-size: var(--tab-header-font-size);
  font-style: var(--tab-header-font-style);
  font-weight: var(--tab-header-font-weight);
  letter-spacing: var(--tab-header-letter-spacing);
  line-height: var(--tab-header-line-height);
  margin-top: -3.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .group {
  height: 35px;
  position: relative;
  width: 337px;
}

.contant2 .group-wrapper {
  height: 15px;
  left: 0;
  position: absolute;
  top: 11px;
  width: 53px;
}

.contant2 .group-2 {
  height: 15px;
  position: relative;
  width: 55px;
}

.contant2 .text-wrapper-content2-3 {
  color: #212121;
  font-family: var(--general-form-elements-medium-font-family);
  font-size: var(--general-form-elements-medium-font-size);
  font-style: var(--general-form-elements-medium-font-style);
  font-weight: var(--general-form-elements-medium-font-weight);
  height: 15px;
  left: 0;
  letter-spacing: var(--general-form-elements-medium-letter-spacing);
  line-height: var(--general-form-elements-medium-line-height);
  position: absolute;
  top: 0;
  white-space: nowrap;
  width: 53px;
}

.contant2 .frame-content2-2 {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  display: flex;
  gap: 10px;
  height: 35px;
  left: 63px;
  padding: 10px 12px;
  position: absolute;
  top: 0;
  width: 274px;
}

.contant2 .p {
  color: #616161;
  font-family: var(--general-default-body-text-font-family);
  font-size: var(--general-default-body-text-font-size);
  font-style: var(--general-default-body-text-font-style);
  font-weight: var(--general-default-body-text-font-weight);
  letter-spacing: var(--general-default-body-text-letter-spacing);
  line-height: var(--general-default-body-text-line-height);
  margin-top: -1.50px;
  position: relative;
  width: 229px;
}

.contant2 .frame-content2-3 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: -0.50px;
  margin-right: -1.00px;
  margin-top: -0.50px;
  position: relative;
  width: 12px;
}

.contant2 .text-wrapper-content2-4 {
  color: #0e5447;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-right: -2.00px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .main-contant2 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 20px;
  position: relative;
}

.contant2 .title-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 5px;
  position: relative;
}

.contant2 .text {
  color: #0e5447;
  font-family: "Roboto-Medium", Helvetica;
  font-size: 18px;
  font-weight: 500;
  height: 21px;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: 153px;
}

.contant2 .info-icon {
  height: 16px;
  position: relative;
  width: 16px;
}

.contant2 .info-circle {
  color: #0e5447;
  font-family: var(--icon-regular-16px-font-family);
  font-size: var(--icon-regular-16px-font-size);
  font-style: var(--icon-regular-16px-font-style);
  font-weight: var(--icon-regular-16px-font-weight);
  height: 16px;
  left: 0;
  letter-spacing: var(--icon-regular-16px-letter-spacing);
  line-height: var(--icon-regular-16px-line-height);
  position: absolute;
  top: -1px;
  white-space: nowrap;
}

.contant2 .section2 {
  align-items: flex-start;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
  padding: 20px;
  position: relative;
  width: 1184px;
}

.contant2 .div-3 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 5px;
  position: relative;
  width: 100%;
}

.contant2 .row-2 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 20px;
  position: relative;
  width: 100%;
}

.contant2 .top-label-dropdown {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 5px;
  position: relative;
  width: 368px;
}

.contant2 .frame-content2-4 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  position: relative;
}

.contant2 .label-wrapper {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  width: 44px;
}

.contant2 .label {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 2px;
  margin-right: -33.00px;
  position: relative;
}

.contant2 .text-wrapper-content2-5 {
  color: #212121;
  font-family: var(--general-form-elements-medium-font-family);
  font-size: var(--general-form-elements-medium-font-size);
  font-style: var(--general-form-elements-medium-font-style);
  font-weight: var(--general-form-elements-medium-font-weight);
  letter-spacing: var(--general-form-elements-medium-letter-spacing);
  line-height: var(--general-form-elements-medium-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .frame-content2-5 {
  align-items: center;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-contant2: center;
  position: relative;
  width: 16px;
}

.contant2 .div-4 {
  align-items: center;
  align-self: stretch;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  display: flex;
  height: 36px;
  justify-contant2: space-between;
  padding: 10px 12px;
  position: relative;
 
}

.contant2 .div-4 select {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
 
  z-index: 2;
}

.contant2 .div-4 .frame-content2-6 {
  pointer-events: none;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.contant2 .frame-content2-7 {
  position: relative;
  display: flex;
  align-items: center;
  background-color: #ffffff;
  border: 1px solid #bdbdbd;
  border-radius: 4px;
  height: 36px;
  padding: 0 12px;
  width: 100%;
}

.contant2 .search-input {
  background: transparent !important;
  border: none !important;
  outline: none !important;
  width: 100% !important;
  height: 100% !important;
  font-family: "Roboto", Helvetica;
  font-size: 14px;
  color: #333;
}

.contant2 .search-input::placeholder {
  color: #999;
}

.contant2 .button .div-wrapper-2 {
  display: flex;
  align-items: center;
  justify-content: center;
}

.contant2 .text-wrapper-content2-6 {
  color: #616161;
  font-family: var(--general-default-body-text-font-family);
  font-size: var(--general-default-body-text-font-size);
  font-style: var(--general-default-body-text-font-style);
  font-weight: var(--general-default-body-text-font-weight);
  letter-spacing: var(--general-default-body-text-letter-spacing);
  line-height: var(--general-default-body-text-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .frame-content2-6 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  width: 12px;
}

.contant2 .help-text-wrapper-content2 {
  align-items: flex-start;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 10px;
  position: relative;
  width: 48px;
}

.contant2 .help-text {
  color: #e86a3a;
  font-family: var(--hyperlink-font-family);
  font-size: var(--hyperlink-font-size);
  font-style: var(--hyperlink-font-style);
  font-weight: var(--hyperlink-font-weight);
  letter-spacing: var(--hyperlink-letter-spacing);
  line-height: var(--hyperlink-line-height);
  margin-right: -15.00px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .search-bar {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 5px;
  justify-contant2: center;
  position: relative;
}

.contant2 .div-5 {
  align-items: flex-end;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 8px;
  position: relative;
}

.contant2 .input-box {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 324px;
}

.contant2 .default {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.contant2 .input-box-top-label {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 5px;
  justify-contant2: center;
  position: relative;
  width: 100%;
}

.contant2 .label-frame-content2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 4px;
  position: relative;
}

.contant2 .label-2 {
  align-items: flex-start;
  align-self: stretch;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 2px;
  position: relative;
}

.contant2 .text-wrapper-content2-7 {
  color: #b71c1c;
  font-family: var(--general-asterisk-font-family);
  font-size: var(--general-asterisk-font-size);
  font-style: var(--general-asterisk-font-style);
  font-weight: var(--general-asterisk-font-weight);
  letter-spacing: var(--general-asterisk-letter-spacing);
  line-height: var(--general-asterisk-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .frame-content2-wrapper {
  align-items: center;
  align-self: stretch;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  display: flex;
  gap: 10px;
  height: 36px;
  justify-contant2: space-around;
  /* padding: 10px 12px; */
  position: relative;
  width: 100%;
}

.contant2 .frame-content2-7 {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 10px;
  position: relative;
}

.contant2 .input {
  background-color: #2c7c6d;
  border: none;
  border-radius: 4px;
  color: #ffffff;
  font-family: "Font Awesome 5 Pro-Solid", Helvetica;
  font-size: 18px;
  font-weight: 400;
  height: 36px;
  letter-spacing: 0;
  line-height: normal;
  overflow: hidden;
  padding: 0;
  position: relative;
  white-space: nowrap;
  width: 36px;
}

.contant2 .button {
  all: unset;
  align-items: center;
  background-color: #2c7c6d;
  border-radius: 20px;
  box-sizing: border-box;
  display: flex;
  gap: 8px;
  height: 36px;
  justify-content: center;
  overflow: hidden;
  padding: 8px 16px;
  position: relative;
  width: 90px;
}

.contant2 .div-wrapper-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-content: center;
  position: relative;
}

.contant2 .PRIMARY {
  color: #ffffff;
  font-family: var(--button-medium-font-family);
  font-size: var(--button-medium-font-size);
  font-style: var(--button-medium-font-style);
  font-weight: var(--button-medium-font-weight);
  letter-spacing: var(--button-medium-letter-spacing);
  line-height: var(--button-medium-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .container {
  height: 839px;
  position: relative;
  width: 1184px;
}

.contant2 .overlap {
  height: 839px;
  position: relative;
}

.contant2 .overlap-wrapper {
  height: 839px;
  left: 0;
  position: absolute;
  top: 0;
  width: 1184px;
}

.contant2 .action-bar {
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  height: 48px;
  left: 0;
  position: absolute;
  top: 0;
  width: 1184px;
}

.contant2 .frame-content2-8 {
  align-items: center;
  display: flex;
  gap: 12px;
  justify-contant2: flex-end;
  left: 900px;
  position: absolute;
  top: 16px;
  width: 264px;
}

.contant2 .element-to-of {
  color: #616161;
  font-family: var(--general-grid-text-font-family);
  font-size: var(--general-grid-text-font-size);
  font-style: var(--general-grid-text-font-style);
  font-weight: var(--general-grid-text-font-weight);
  letter-spacing: var(--general-grid-text-letter-spacing);
  line-height: var(--general-grid-text-line-height);
  margin-top: -1.00px;
  position: relative;
  text-align: right;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .data-grid-action-bar {
  align-items: center;
  display: inline-flex;
  gap: 8px;
  left: 20px;
  position: absolute;
  top: 8px;
}

.contant2 .button-2 {
  all: unset;
  align-items: center;
  background-color: #e0e0e0;
  border-radius: 18px;
  box-sizing: border-box;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 6px;
  height: 32px;
  justify-contant2: center;
  overflow: hidden;
  padding: 8px 16px;
  position: relative;
}

.contant2 .text-wrapper-content2-8 {
  color: #9e9e9e;
  font-family: var(--button-small-font-family);
  font-size: var(--button-small-font-size);
  font-style: var(--button-small-font-style);
  font-weight: var(--button-small-font-weight);
  letter-spacing: var(--button-small-letter-spacing);
  line-height: var(--button-small-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .button-3 {
  all: unset;
  align-items: center;
  background-color: #eeeeee;
  border-radius: 18px;
  box-sizing: border-box;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 6px;
  height: 32px;
  justify-contant2: center;
  overflow: hidden;
  padding: 8px 16px;
  position: relative;
}

.contant2 .container-wrapper {
  height: 810px;
  left: 0;
  position: absolute;
  top: 29px;
  width: 1184px;
}

.contant2 .overlap-group-wrapper {
  height: 810px;
}

.contant2 .overlap-group {
  height: 810px;
  position: relative;
  width: 1184px;
}

.contant2 .line-38 {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 809px !important;
  width: 1184px !important;
}

.contant2 .line-39 {
  height: 810px !important;
  left: 1183px !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  width: 1px !important;
}

.contant2 .container-2 {
  height: 792px;
  left: 0;
  position: absolute;
  top: 47px;
  width: 1184px;
}

.contant2 .overlap-2 {
  height: 792px;
  position: relative;
}

.contant2 .data-grids {
  border: 1px solid;
  border-color: #000000;
  height: 790px;
  left: 0;
  position: absolute;
  top: 1px;
  width: 1184px;
}

.contant2 .grids-data-grid {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 25px;
  position: relative;
  width: 1184px;
}

.contant2 .container-3 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex-direction: column;
  height: 790px;
  position: relative;
  width: 100%;
}

.contant2 .container-4 {
  align-items: flex-start;
  background-color: #ffffff;
  display: flex;
  flex: 0 0 auto;
  position: relative;
  width: 1184px;
}

.contant2 .line-wrapper {
  align-self: stretch;
  background-color: #bdbdbd;
  position: relative;
  width: 1px;
}

.contant2 .line-7 {
  height: 39px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  width: 1px !important;
}

.contant2 .checkbox-column {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  position: relative;
}

.contant2 .checkbox {
  align-items: center;
  background-color: #67625b;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-color: #bdbdbd;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 4px;
  min-height: 30px;
  padding: 12px 18px;
  position: relative;
}

.contant2 .rectangle {
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 3px;
  height: 16px;
  position: relative;
  width: 16px;
}

.contant2 .checkbox-states {
  align-items: center;
  background-color: #f5f5f5;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 4px;
  min-height: 30px;
  padding: 7px 18px;
  position: relative;
}

.contant2 .check-box-wrapper {
  align-items: center;
  background-color: #ffffff;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 4px;
  min-height: 30px;
  padding: 7px 18px;
  position: relative;
}

.contant2 .checkbox-states-wrapper {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 10px;
  position: relative;
}

.contant2 .data-column {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 192px;
}

.contant2 .header-cell {
  align-items: center;
  align-self: stretch;
  background-color: #67625b;
  border-bottom-style: solid;
  border-bottom-width: 1px;
  border-color: #bdbdbd;
  display: flex;
  flex: 0 0 auto;
  gap: 8px;
  min-height: 40px;
  min-width: 120px;
  padding: 8px 14px 8px 0px;
  position: relative;
  width: 100%;
}

.contant2 .frame-content2-9 {
  align-items: flex-end;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 16px;
  position: relative;
}

.contant2 .group-3 {
  height: 20px;
  margin-right: -2.00px;
  position: relative;
  width: 95px;
}

.contant2 .line-1 {
  height: 20px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  width: 1px !important;
}

.contant2 .label-3 {
  color: #ffffff;
  font-family: var(--general-grid-text-font-family);
  font-size: var(--general-grid-text-font-size);
  font-style: var(--general-grid-text-font-style);
  font-weight: var(--general-grid-text-font-weight);
  left: 16px;
  letter-spacing: var(--general-grid-text-letter-spacing);
  line-height: var(--general-grid-text-line-height);
  position: absolute;
  top: 2px;
  white-space: nowrap;
}

.contant2 .cell-states {
  align-items: center;
  align-self: stretch;
  background-color: #f5f5f5;
  display: flex;
  flex: 0 0 auto;
  gap: 4px;
  min-height: 30px;
  padding: 7px 10px 7px 16px;
  position: relative;
  width: 100%;
}

.contant2 .lorem-lipsum {
  color: #616161;
  font-family: var(--general-grid-text-font-family);
  font-size: var(--general-grid-text-font-size);
  font-style: var(--general-grid-text-font-style);
  font-weight: var(--general-grid-text-font-weight);
  letter-spacing: var(--general-grid-text-letter-spacing);
  line-height: var(--general-grid-text-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .cell-states-2 {
  align-items: center;
  align-self: stretch;
  background-color: #ffffff;
  display: flex;
  flex: 0 0 auto;
  gap: 4px;
  min-height: 30px;
  padding: 7px 10px 7px 16px;
  position: relative;
  width: 100%;
}

.contant2 .group-4 {
  height: 20px;
  margin-right: -2.00px;
  position: relative;
  width: 80px;
}

.contant2 .data-column-2 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 217px;
}

.contant2 .group-5 {
  height: 20px;
  margin-right: -2.00px;
  position: relative;
  width: 119px;
}

.contant2 .data-column-3 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 301px;
}

.contant2 .lorem-lipsum-2 {
  color: #ffffff;
  font-family: var(--general-grid-text-font-family);
  font-size: var(--general-grid-text-font-size);
  font-style: var(--general-grid-text-font-style);
  font-weight: var(--general-grid-text-font-weight);
  letter-spacing: var(--general-grid-text-letter-spacing);
  line-height: var(--general-grid-text-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .lorem-lipsum-3 {
  color: #f5f5f5;
  font-family: var(--general-grid-text-font-family);
  font-size: var(--general-grid-text-font-size);
  font-style: var(--general-grid-text-font-style);
  font-weight: var(--general-grid-text-font-weight);
  letter-spacing: var(--general-grid-text-letter-spacing);
  line-height: var(--general-grid-text-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .data-column-4 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 229px;
}

.contant2 .container-5 {
  align-self: stretch;
  flex: 0 0 auto;
  margin-bottom: -1.00px;
  position: relative;
  width: 100%;
}

.contant2 .line-40 {
  height: 792px !important;
  left: 1183px !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  width: 1px !important;
}

.contant2 .pagination {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  height: 60px;
  justify-content: space-around;
  padding: 24px 0px 0px;
  position: relative;
  /* width: 1184px; */
}

.contant2 .pagination-info {
  font-family: "Roboto", Helvetica;
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.contant2 .pagination-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.contant2 .pagination-btn {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #333;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-family: "Roboto", Helvetica;
  font-size: 14px;
  transition: all 0.2s ease;
}

.contant2 .pagination-btn:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.contant2 .pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.contant2 .page-numbers {
  display: flex;
  align-items: center;
  gap: 2px;
}

.contant2 .page-number {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  color: #333;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-family: "Roboto", Helvetica;
  font-size: 14px;
  transition: all 0.2s ease;
  min-width: 40px;
  text-align: center;
}

.contant2 .page-number:hover {
  background-color: #e9ecef;
  border-color: #adb5bd;
}

.contant2 .page-number.active {
  background-color: #007bff !important;
  color: white !important;
  border-color: #007bff !important;
}

.contant2 .frame-content2-new {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.contant2 .frame-content2-11 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  position: relative;
}

.contant2 .frame-content2-12 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 24px;
  position: relative;
}

.contant2 .text-wrapper-content2-9 {
  color: #616161;
  font-family: var(--general-navigation-default-font-family);
  font-size: var(--general-navigation-default-font-size);
  font-style: var(--general-navigation-default-font-style);
  font-weight: var(--general-navigation-default-font-weight);
  letter-spacing: var(--general-navigation-default-letter-spacing);
  line-height: var(--general-navigation-default-line-height);
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .frame-content2-13 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 12px;
  position: relative;
}

.contant2 .default-label {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  position: relative;
  width: 62px;
}

.contant2 .text-wrapper-content2-10 {
  color: #0e5447;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  text-align: right;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .frame-content2-14 {
  align-items: flex-end;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 10px;
  justify-contant2: center;
  position: relative;
}

.contant2 .component {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px 0px 0px 4px;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  height: 36px;
  justify-contant2: center;
  overflow: hidden;
  padding: 12px 11px;
  position: relative;
}

.contant2 .frame-content2-15 {
  align-items: center;
  display: flex;
  gap: 10px;
  justify-contant2: center;
  margin-bottom: -1.00px;
  margin-top: -1.00px;
  position: relative;
  transform: rotate(-180deg);
  width: 12px;
}

.contant2 .text-wrapper-content2-11 {
  color: #bdbdbd;
  font-family: "Font Awesome 5 Pro-Solid", Helvetica;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-left: -1.00px;
  margin-right: -1.00px;
  margin-top: -1.00px;
  position: relative;
  transform: rotate(180deg);
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .component-2 {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  height: 36px;
  justify-contant2: center;
  padding: 12px 11px;
  position: relative;
}

.contant2 .text-wrapper-content2-12 {
  color: #bdbdbd;
  font-family: "Font Awesome 5 Pro-Solid", Helvetica;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-left: -0.50px;
  margin-right: -0.50px;
  margin-top: -1.00px;
  position: relative;
  transform: rotate(180deg);
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .component-3 {
  align-items: center;
  background-color: #0e5447;
  border: 1px solid;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-contant2: center;
  padding: 10px 11px;
  position: relative;
}

.contant2 .frame-content2-16 {
  align-items: center;
  display: flex;
  gap: 10px;
  justify-contant2: center;
  position: relative;
  width: 12px;
}

.contant2 .text-wrapper-content2-13 {
  color: #ffffff;
  font-family: var(--general-navigation-default-font-family);
  font-size: var(--general-navigation-default-font-size);
  font-style: var(--general-navigation-default-font-style);
  font-weight: var(--general-navigation-default-font-weight);
  letter-spacing: var(--general-navigation-default-letter-spacing);
  line-height: var(--general-navigation-default-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .component-4 {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-contant2: center;
  padding: 10px 11px;
  position: relative;
}

.contant2 .element {
  color: #0e5447;
  font-family: var(--general-navigation-default-font-family);
  font-size: var(--general-navigation-default-font-size);
  font-style: var(--general-navigation-default-font-style);
  font-weight: var(--general-navigation-default-font-weight);
  letter-spacing: var(--general-navigation-default-letter-spacing);
  line-height: var(--general-navigation-default-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .component-5 {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  height: 36px;
  justify-contant2: center;
  padding: 12px 11px;
  position: relative;
  transform: rotate(-180deg);
}

.contant2 .text-wrapper-content2-14 {
  color: #0e5447;
  font-family: "Font Awesome 5 Pro-Solid", Helvetica;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-left: -0.50px;
  margin-right: -0.50px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .component-6 {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 0px 4px 4px 0px;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  height: 36px;
  justify-contant2: center;
  overflow: hidden;
  padding: 12px 11px;
  position: relative;
}

.contant2 .frame-content2-17 {
  align-items: center;
  display: flex;
  gap: 10px;
  justify-contant2: center;
  margin-bottom: -1.00px;
  margin-top: -1.00px;
  position: relative;
  width: 12px;
}

.contant2 .text-wrapper-content2-15 {
  color: #0e5447;
  font-family: "Font Awesome 5 Pro-Solid", Helvetica;
  font-size: 14px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-left: -1.00px;
  margin-right: -1.00px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .component-7 {
  background-color: #f5ead9;
  flex: 0 0 auto;
  position: relative;
  width: 1280px;
}

.contant2 .frame-content2-18 {
  align-items: center;
  display: inline-flex;
  gap: 12px;
  justify-contant2: center;
  left: 470px;
  position: relative;
  top: 11px;
}

.contant2 .frame-content2-19 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  padding: 0px 0px 0px 12px;
  position: relative;
}

.contant2 .text-wrapper-content2-16 {
  color: #0e5447;
  font-family: var(--footer-hyperlink-font-family);
  font-size: var(--footer-hyperlink-font-size);
  font-style: var(--footer-hyperlink-font-style);
  font-weight: var(--footer-hyperlink-font-weight);
  letter-spacing: var(--footer-hyperlink-letter-spacing);
  line-height: var(--footer-hyperlink-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-contant2;
}

.contant2 .line-1-instance {
  height: 16px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  width: 1px !important;
}

/* Modal Styles */
.contant2 .modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
}

.contant2 .modal-content {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 90vw;
  max-height: 90vh;
  width: 1200px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.contant2 .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  background: #f8f9fa;
}

.contant2 .modal-title {
  margin: 0;
  color: #0e5447;
  font-family: "Roboto-Medium", Helvetica;
  font-size: 18px;
  font-weight: 500;
}

.contant2 .modal-close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.contant2 .modal-close-btn:hover {
  background-color: #f0f0f0;
}

.contant2 .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

/* Responsive modal styles */
@media (max-width: 768px) {
  .contant2 .modal-overlay {
    padding: 10px;
  }

  .contant2 .modal-content {
    width: 100%;
    max-width: 100%;
    max-height: 95vh;
  }

  .contant2 .modal-header {
    padding: 16px 20px;
  }

  .contant2 .modal-title {
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .contant2 .modal-overlay {
    padding: 5px;
  }

  .contant2 .modal-content {
    max-height: 98vh;
  }

  .contant2 .modal-header {
    padding: 12px 16px;
  }
}
